# Python 后端文件分析文档

本文档详细分析了 Python 后端 (`backend/src/agent/`) 目录下各个文件的作用、实现逻辑和核心功能。

## 文件概览

```
backend/src/agent/
├── __init__.py
├── app.py              # FastAPI 应用入口
├── configuration.py    # 配置管理
├── graph.py           # LangGraph 状态图核心逻辑
├── prompts.py         # 提示词模板
├── state.py           # 状态定义
├── tools_and_schemas.py # 数据模型
└── utils.py           # 工具函数
```

---

## 1. `state.py` - 状态定义

### 作用
定义 LangGraph 状态图中的所有状态类型，管理整个工作流的数据流转。

### 核心状态类型

#### `OverallState` - 主状态
贯穿整个工作流的核心状态，包含：

```python
class OverallState(TypedDict):
    messages: Annotated[list, add_messages]           # 对话消息列表 (自动合并)
    search_query: Annotated[list, operator.add]       # 搜索查询列表 (自动累加)
    web_research_result: Annotated[list, operator.add] # 网络搜索结果 (自动累加)
    sources_gathered: Annotated[list, operator.add]   # 收集的来源 (自动累加)
    initial_search_query_count: int                   # 初始搜索查询数量
    max_research_loops: int                           # 最大研究循环次数
    research_loop_count: int                          # 当前研究循环计数
    reasoning_model: str                              # 推理模型名称
```

#### `ReflectionState` - 反思阶段状态
用于知识缺口分析和后续查询生成：

```python
class ReflectionState(TypedDict):
    is_sufficient: bool                               # 信息是否充足
    knowledge_gap: str                                # 知识缺口描述
    follow_up_queries: Annotated[list, operator.add] # 后续查询列表
    research_loop_count: int                          # 研究循环计数
    number_of_ran_queries: int                        # 已运行查询数量
```

#### 其他辅助状态
- `QueryGenerationState`: 查询生成状态
- `WebSearchState`: 网络搜索状态  
- `SearchStateOutput`: 搜索输出状态

### 关键特性
- 使用 `Annotated` 和 `operator.add`/`add_messages` 实现状态自动合并
- 支持 LangGraph 的状态管理机制

---

## 2. `tools_and_schemas.py` - 数据模型

### 作用
定义结构化输出的 Pydantic 模型，确保 LLM 返回格式化的 JSON 响应。

### 核心模型

#### `SearchQueryList` - 搜索查询列表模型
```python
class SearchQueryList(BaseModel):
    query: List[str] = Field(description="搜索查询字符串列表")
    rationale: str = Field(description="查询策略的简要说明")
```

#### `Reflection` - 反思结果模型
```python
class Reflection(BaseModel):
    is_sufficient: bool = Field(description="信息是否充足")
    knowledge_gap: str = Field(description="缺失信息描述")
    follow_up_queries: List[str] = Field(description="后续查询列表")
```

### 用途
与 LangChain 的 `with_structured_output()` 配合使用，确保 LLM 输出符合预期格式。

---

## 3. `configuration.py` - 配置管理

### 作用
管理 Agent 的配置参数，支持环境变量和运行时配置。

### 核心配置项

#### 模型配置
```python
query_generator_model: str = "gemini-2.0-flash"              # 查询生成模型
reflection_model: str = "gemini-2.5-flash-preview-04-17"     # 反思模型
answer_model: str = "gemini-2.5-pro-preview-05-06"           # 最终答案模型
```

#### 行为配置
```python
number_of_initial_queries: int = 3    # 初始查询数量
max_research_loops: int = 2           # 最大研究循环次数
```

### 配置优先级
环境变量 > 运行时配置 > 默认值

### 关键方法
- `from_runnable_config()`: 从 LangGraph 的 RunnableConfig 创建配置实例
- 支持动态配置覆盖

---

## 4. `utils.py` - 工具函数

### 作用
提供核心工具函数，处理消息解析、URL 管理和引用生成。

### 核心函数

#### `get_research_topic(messages)` - 提取研究主题
```python
# 单条消息：直接返回内容
# 多条消息：格式化为 "User: xxx\nAssistant: xxx\n" 的对话历史
```

#### `resolve_urls(urls_to_resolve, id)` - URL 缩短
```python
# 将长 URL 映射为短 URL
# 格式：https://vertexaisearch.cloud.google.com/id/{id}-{idx}
# 确保每个 URL 有唯一的缩短形式
```

#### `insert_citation_markers(text, citations_list)` - 插入引用标记
```python
# 在文本中插入 Markdown 格式的引用链接
# 从后往前插入，避免索引偏移问题
# 格式：[标题](短链接)
```

#### `get_citations(response, resolved_urls_map)` - 提取引用信息
```python
# 从 Gemini 响应的 grounding_metadata 中提取引用
# 生成包含位置信息和链接的引用对象
# 处理 Google Search API 的 grounding 数据
```

### 关键特性
- 专门处理 Google Search API 的 grounding metadata
- 实现自动引用生成和链接管理
- 支持多轮对话的上下文提取

---

## 5. `prompts.py` - 提示词模板

### 作用
定义各个阶段的提示词模板，指导 LLM 在不同节点的行为。

### 核心提示词

#### `query_writer_instructions` - 查询生成提示
- **目标**: 生成多样化的网络搜索查询
- **要求**: 最多 {number_queries} 个查询，关注时效性，避免重复
- **输出**: JSON 格式 `{"rationale": "...", "query": ["...", "..."]}`

#### `web_searcher_instructions` - 网络搜索提示  
- **目标**: 进行有针对性的 Google 搜索
- **要求**: 收集最新可信信息，追踪来源，生成总结报告
- **输出**: 基于搜索结果的结构化总结

#### `reflection_instructions` - 反思提示
- **目标**: 识别知识缺口，决定是否需要更多研究
- **要求**: 判断信息充足性，生成补充查询
- **输出**: JSON 格式 `{"is_sufficient": bool, "knowledge_gap": "...", "follow_up_queries": [...]}`

#### `answer_instructions` - 最终答案提示
- **目标**: 基于所有研究结果生成高质量答案
- **要求**: 必须包含所有引用，保持专业性
- **输出**: 带完整引用的最终答案

### 关键特性
- 所有提示词都强调时效性 (使用 {current_date})
- 严格要求引用准确性和来源追踪
- 支持结构化 JSON 输出格式

---

## 总结

这五个文件构成了 LangGraph Agent 的基础架构：

1. **`state.py`**: 定义数据流转的状态结构
2. **`tools_and_schemas.py`**: 确保 LLM 输出格式化
3. **`configuration.py`**: 管理系统配置和模型选择
4. **`utils.py`**: 提供核心工具函数支持
5. **`prompts.py`**: 指导各阶段的 LLM 行为

---

## 6. `graph.py` - LangGraph 状态图核心逻辑

### 作用
实现完整的 LangGraph 状态图，定义 AI Agent 的工作流程和节点逻辑。

### 状态图结构
```
START → generate_query → web_research → reflection → finalize_answer → END
            ↓              ↑              ↓
            └──────────────┴──────────────┘ (循环)
```

### 核心节点实现

#### `generate_query()` - 查询生成节点
**功能**: 基于用户问题生成多个搜索查询

```python
def generate_query(state: OverallState, config: RunnableConfig) -> QueryGenerationState:
    # 1. 获取配置 (模型、查询数量等)
    configurable = Configuration.from_runnable_config(config)

    # 2. 初始化 Gemini 2.0 Flash 模型
    llm = ChatGoogleGenerativeAI(model=configurable.query_generator_model, temperature=1.0)
    structured_llm = llm.with_structured_output(SearchQueryList)

    # 3. 格式化提示词 (包含当前日期、研究主题、查询数量)
    formatted_prompt = query_writer_instructions.format(...)

    # 4. 生成结构化查询列表
    result = structured_llm.invoke(formatted_prompt)
    return {"query_list": result.query}
```

#### `continue_to_web_research()` - 并行分发函数
**功能**: 将查询列表分发到多个并行的 web_research 节点

```python
def continue_to_web_research(state: QueryGenerationState):
    return [
        Send("web_research", {"search_query": search_query, "id": int(idx)})
        for idx, search_query in enumerate(state["query_list"])
    ]
```

#### `web_research()` - 网络搜索节点
**功能**: 执行 Google 搜索并生成带引用的总结

```python
def web_research(state: WebSearchState, config: RunnableConfig) -> OverallState:
    # 1. 使用 Google GenAI 客户端进行搜索 (支持 grounding metadata)
    response = genai_client.models.generate_content(
        model=configurable.query_generator_model,
        contents=formatted_prompt,
        config={"tools": [{"google_search": {}}], "temperature": 0}
    )

    # 2. 处理搜索结果
    resolved_urls = resolve_urls(response.candidates[0].grounding_metadata.grounding_chunks, state["id"])
    citations = get_citations(response, resolved_urls)
    modified_text = insert_citation_markers(response.text, citations)

    # 3. 返回搜索结果和来源
    return {
        "sources_gathered": sources_gathered,
        "search_query": [state["search_query"]],
        "web_research_result": [modified_text]
    }
```

#### `reflection()` - 反思节点
**功能**: 分析当前研究结果，判断是否需要更多信息

```python
def reflection(state: OverallState, config: RunnableConfig) -> ReflectionState:
    # 1. 增加研究循环计数
    state["research_loop_count"] = state.get("research_loop_count", 0) + 1

    # 2. 使用反思模型分析现有总结
    llm = ChatGoogleGenerativeAI(model=reasoning_model, temperature=1.0)
    result = llm.with_structured_output(Reflection).invoke(formatted_prompt)

    # 3. 返回反思结果
    return {
        "is_sufficient": result.is_sufficient,
        "knowledge_gap": result.knowledge_gap,
        "follow_up_queries": result.follow_up_queries,
        "research_loop_count": state["research_loop_count"],
        "number_of_ran_queries": len(state["search_query"])
    }
```

#### `evaluate_research()` - 路由决策函数
**功能**: 决定下一步是继续研究还是生成最终答案

```python
def evaluate_research(state: ReflectionState, config: RunnableConfig):
    # 检查停止条件: 信息充足 OR 达到最大循环次数
    if state["is_sufficient"] or state["research_loop_count"] >= max_research_loops:
        return "finalize_answer"
    else:
        # 继续研究: 为每个后续查询创建新的 web_research 节点
        return [
            Send("web_research", {
                "search_query": follow_up_query,
                "id": state["number_of_ran_queries"] + int(idx)
            })
            for idx, follow_up_query in enumerate(state["follow_up_queries"])
        ]
```

#### `finalize_answer()` - 最终答案节点
**功能**: 基于所有研究结果生成最终答案

```python
def finalize_answer(state: OverallState, config: RunnableConfig):
    # 1. 使用答案生成模型
    llm = ChatGoogleGenerativeAI(model=reasoning_model, temperature=0)
    result = llm.invoke(formatted_prompt)

    # 2. 处理引用链接 (将短链接替换为原始链接)
    unique_sources = []
    for source in state["sources_gathered"]:
        if source["short_url"] in result.content:
            result.content = result.content.replace(source["short_url"], source["value"])
            unique_sources.append(source)

    # 3. 返回最终消息
    return {
        "messages": [AIMessage(content=result.content)],
        "sources_gathered": unique_sources
    }
```

### 状态图构建
```python
# 创建状态图
builder = StateGraph(OverallState, config_schema=Configuration)

# 添加节点
builder.add_node("generate_query", generate_query)
builder.add_node("web_research", web_research)
builder.add_node("reflection", reflection)
builder.add_node("finalize_answer", finalize_answer)

# 定义边和条件路由
builder.add_edge(START, "generate_query")
builder.add_conditional_edges("generate_query", continue_to_web_research, ["web_research"])
builder.add_edge("web_research", "reflection")
builder.add_conditional_edges("reflection", evaluate_research, ["web_research", "finalize_answer"])
builder.add_edge("finalize_answer", END)

# 编译图
graph = builder.compile(name="pro-search-agent")
```

### 关键特性
- **并行处理**: 使用 `Send()` 机制并行执行多个搜索查询
- **条件路由**: 根据反思结果决定是否继续研究
- **状态管理**: 自动合并和累积状态更新
- **引用处理**: 完整的引用提取和链接管理
- **模型选择**: 不同阶段使用不同的 Gemini 模型

---

## 7. `app.py` - FastAPI 应用入口

### 作用
定义 FastAPI 应用，主要负责前端静态文件服务。LangGraph API 由 LangGraph 服务器自动提供。

### 核心功能

#### FastAPI 应用初始化
```python
app = FastAPI()  # 创建 FastAPI 实例
```

#### `create_frontend_router()` - 前端路由创建
**功能**: 创建服务 React 前端的路由器

```python
def create_frontend_router(build_dir="../frontend/dist"):
    # 1. 计算前端构建目录路径
    build_path = pathlib.Path(__file__).parent.parent.parent / build_dir
    static_files_path = build_path / "assets"  # Vite 使用 'assets' 子目录

    # 2. 检查前端构建是否存在
    if not build_path.is_dir() or not (build_path / "index.html").is_file():
        # 返回错误页面
        async def dummy_frontend(request):
            return Response(
                "Frontend not built. Run 'npm run build' in the frontend directory.",
                media_type="text/plain", status_code=503
            )
        return Route("/{path:path}", endpoint=dummy_frontend)

    # 3. 创建前端应用
    react = FastAPI(openapi_url="")  # 禁用 OpenAPI 文档

    # 4. 挂载静态资源
    react.mount("/assets", StaticFiles(directory=static_files_path), name="static_assets")

    # 5. 处理所有路由 (SPA 路由)
    @react.get("/{path:path}")
    async def handle_catch_all(request: Request, path: str):
        fp = build_path / path
        if not fp.exists() or not fp.is_file():
            fp = build_path / "index.html"  # 回退到 index.html
        return fastapi.responses.FileResponse(fp)

    return react
```

#### 前端挂载
```python
# 将前端挂载到 /app 路径，避免与 LangGraph API 路由冲突
app.mount("/app", create_frontend_router(), name="frontend")
```

### 部署架构

#### LangGraph 配置 (`langgraph.json`)
```json
{
  "dependencies": ["."],
  "graphs": {
    "agent": "./src/agent/graph.py:graph"  // 状态图定义
  },
  "http": {
    "app": "./src/agent/app.py:app"        // FastAPI 应用
  },
  "env": ".env"                            // 环境变量文件
}
```

#### 路由结构
- **LangGraph API**: 自动生成的 API 端点 (如 `/runs`, `/threads` 等)
- **前端应用**: `/app/*` - React SPA
- **静态资源**: `/app/assets/*` - CSS, JS, 图片等

### 关键特性
- **SPA 支持**: 所有未匹配的路由都回退到 `index.html`
- **静态资源服务**: 正确处理 Vite 构建的资源结构
- **错误处理**: 前端未构建时显示友好错误信息
- **路径分离**: 前端和 API 路径完全分离，避免冲突

---

## 总结

现在我们已经完整分析了 Python 后端的所有核心文件：

### 架构概览
1. **`state.py`**: 定义状态结构和数据流
2. **`tools_and_schemas.py`**: 确保结构化输出
3. **`configuration.py`**: 管理配置和模型选择
4. **`utils.py`**: 提供工具函数支持
5. **`prompts.py`**: 定义各阶段提示词
6. **`graph.py`**: 实现完整的 LangGraph 工作流 ⭐
7. **`app.py`**: 提供 FastAPI 前端服务

### 工作流程
```
用户请求 → generate_query → [web_research × N] → reflection →
         ↑                                                    ↓
         └─────────── 循环 (如果信息不足) ←─────────────────────┘
                                                              ↓
                                                      finalize_answer → 返回结果
```

### 技术特点
- **并行搜索**: 同时执行多个搜索查询
- **智能反思**: 自动判断信息充足性
- **完整引用**: 自动提取和管理引用链接
- **模型优化**: 不同阶段使用最适合的模型
- **状态管理**: LangGraph 自动处理状态合并

这个架构为 JavaScript 实现提供了完整的参考蓝图！
