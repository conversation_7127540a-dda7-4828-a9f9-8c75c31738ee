# Google Gemini API Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# Model Configuration
QUERY_GENERATOR_MODEL=gemini-2.0-flash
REFLECTION_MODEL=gemini-2.5-flash-preview-04-17
ANSWER_MODEL=gemini-2.5-pro-preview-05-06

# Agent Behavior Configuration
NUMBER_OF_INITIAL_QUERIES=3
MAX_RESEARCH_LOOPS=2

# Server Configuration
PORT=3001
NODE_ENV=development

# Logging Configuration
LOG_LEVEL=info

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
