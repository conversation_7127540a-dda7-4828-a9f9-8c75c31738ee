{"name": "langgraph-agent-js", "version": "1.0.0", "description": "JavaScript/TypeScript implementation of LangGraph research agent", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration", "lint": "eslint src tests --ext .ts", "lint:fix": "eslint src tests --ext .ts --fix", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "prepare": "npm run build"}, "dependencies": {"@langchain/langgraph": "^0.2.19", "@langchain/core": "^0.3.15", "@langchain/google-genai": "^0.1.2", "@google/generative-ai": "^0.21.0", "express": "^4.21.1", "cors": "^2.8.5", "helmet": "^8.0.0", "dotenv": "^16.4.5", "zod": "^3.23.8", "winston": "^3.15.0"}, "devDependencies": {"@types/node": "^22.9.0", "@types/express": "^5.0.0", "@types/cors": "^2.8.17", "@types/jest": "^29.5.14", "@typescript-eslint/eslint-plugin": "^8.14.0", "@typescript-eslint/parser": "^8.14.0", "eslint": "^9.14.0", "jest": "^29.7.0", "ts-jest": "^29.2.5", "tsx": "^4.19.2", "typescript": "^5.6.3", "rimraf": "^6.0.1", "msw": "^2.6.4", "supertest": "^7.0.0", "@types/supertest": "^6.0.2"}, "engines": {"node": ">=18.0.0"}, "keywords": ["langgraph", "langchain", "ai", "agent", "research", "typescript", "javascript"], "author": "Your Name", "license": "MIT"}