/**
 * State definitions for LangGraph research agent
 * 
 * This file defines all state types used throughout the agent workflow,
 * corresponding to the Python state.py file.
 */

import { Annotation, messagesStateReducer } from "@langchain/langgraph";
import type { BaseMessage } from "@langchain/core/messages";

// Source interface for citation tracking
export interface Source {
  label: string;
  shortUrl: string;
  value: string;
}

// Query interface for search queries
export interface Query {
  query: string;
  rationale: string;
}

/**
 * Main state that persists throughout the entire workflow
 * Corresponds to Python OverallState
 */
export const OverallState = Annotation.Root({
  // Messages between user and agent
  messages: Annotation<BaseMessage[]>({
    reducer: messagesStateReducer, // Equivalent to add_messages in Python
    default: () => [],
  }),

  // Search queries generated and executed
  searchQuery: Annotation<string[]>({
    reducer: (state: string[], update: string[]) => state.concat(update), // Equivalent to operator.add
    default: () => [],
  }),

  // Results from web research
  webResearchResult: Annotation<string[]>({
    reducer: (state: string[], update: string[]) => state.concat(update),
    default: () => [],
  }),

  // Sources gathered during research
  sourcesGathered: Annotation<Source[]>({
    reducer: (state: Source[], update: Source[]) => state.concat(update),
    default: () => [],
  }),

  // Configuration and control parameters
  initialSearchQueryCount: Annotation<number>(),
  maxResearchLoops: Annotation<number>(),
  researchLoopCount: Annotation<number>(),
  reasoningModel: Annotation<string>(),
});

/**
 * State for reflection phase
 * Corresponds to Python ReflectionState
 */
export const ReflectionState = Annotation.Root({
  isSufficient: Annotation<boolean>(),
  knowledgeGap: Annotation<string>(),
  followUpQueries: Annotation<string[]>({
    reducer: (state: string[], update: string[]) => state.concat(update),
    default: () => [],
  }),
  researchLoopCount: Annotation<number>(),
  numberOfRanQueries: Annotation<number>(),
});

/**
 * State for query generation phase
 * Corresponds to Python QueryGenerationState
 */
export const QueryGenerationState = Annotation.Root({
  queryList: Annotation<Query[]>(),
});

/**
 * State for individual web search operations
 * Corresponds to Python WebSearchState
 */
export const WebSearchState = Annotation.Root({
  searchQuery: Annotation<string>(),
  id: Annotation<number>(),
});

/**
 * Output state for search operations
 * Corresponds to Python SearchStateOutput dataclass
 */
export interface SearchStateOutput {
  runningSummary?: string;
  sources?: Source[];
  searchQuery?: string;
  id?: number;
}

// Type exports for use in other modules
export type OverallStateType = typeof OverallState.State;
export type ReflectionStateType = typeof ReflectionState.State;
export type QueryGenerationStateType = typeof QueryGenerationState.State;
export type WebSearchStateType = typeof WebSearchState.State;
