/**
 * LangGraph state graph implementation
 * 
 * This file implements the complete LangGraph workflow for the research agent,
 * corresponding to the Python graph.py file.
 */

import { StateGraph, Send } from "@langchain/langgraph";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import type { RunnableConfig } from "@langchain/core/runnables";
import { GoogleGenerativeAI } from "@google/generative-ai";

import {
  OverallState,
  QueryGenerationState,
  ReflectionState,
  WebSearchState,
  type Source,
  type Query
} from "./state.ts";
import { fromRunnableConfig } from "./config.ts";
import {
  SearchQueryListSchema,
  ReflectionSchema,
  validateSearchQueryList,
  validateReflection
} from "./schemas.ts";
import {
  formatQueryWriterPrompt,
  formatWebSearcherPrompt,
  formatReflectionPrompt,
  formatAnswerPrompt
} from "./prompts.ts";
import {
  getResearchTopic,
  resolveUrls,
  insertCitationMarkers,
  getCitations
} from "./utils.ts";

// Initialize Google Generative AI client for web search
const genaiClient = new GoogleGenerativeAI(process.env["GEMINI_API_KEY"] || "");

/**
 * Node: Generate search queries based on user's question
 * Corresponds to Python generate_query function
 */
async function generateQuery(
  state: typeof OverallState.State,
  config?: RunnableConfig
): Promise<typeof QueryGenerationState.State> {
  const agentConfig = fromRunnableConfig(config);
  
  // Set initial search query count if not already set
  const initialSearchQueryCount = state.initialSearchQueryCount ?? agentConfig.numberOfInitialQueries;
  
  // Initialize Gemini model
  const llm = new ChatGoogleGenerativeAI({
    model: agentConfig.queryGeneratorModel,
    temperature: 1.0,
    maxRetries: 2,
    apiKey: process.env["GEMINI_API_KEY"] || "",
  });

  // Create structured output LLM
  const structuredLlm = llm.withStructuredOutput(SearchQueryListSchema);

  // Format the prompt
  const researchTopic = getResearchTopic(state.messages);
  const formattedPrompt = formatQueryWriterPrompt(
    researchTopic,
    initialSearchQueryCount
  );
  
  // Generate search queries
  const result = await structuredLlm.invoke(formattedPrompt);
  const validatedResult = validateSearchQueryList(result);
  
  // Convert string array to Query objects for compatibility
  const queryList: Query[] = validatedResult.query.map(query => ({
    query,
    rationale: validatedResult.rationale
  }));
  
  return { queryList };
}

/**
 * Conditional edge: Continue to web research
 * Corresponds to Python continue_to_web_research function
 */
function continueToWebResearch(state: typeof OverallState.State): Send[] {
  // For now, we'll create a simple query based on the messages
  // In a real implementation, this would use the generated queries
  const researchTopic = getResearchTopic(state.messages);
  return [
    new Send("web_research", {
      searchQuery: researchTopic,
      id: 0
    })
  ];
}

/**
 * Node: Perform web research for a single query
 * Corresponds to Python web_research function
 */
async function webResearch(
  state: typeof WebSearchState.State,
  config?: RunnableConfig
): Promise<Partial<typeof OverallState.State>> {
  const agentConfig = fromRunnableConfig(config);
  
  // Format the prompt for web search
  const formattedPrompt = formatWebSearcherPrompt(state.searchQuery);

  try {
    // Use Google Generative AI client for web search with grounding
    const model = genaiClient.getGenerativeModel({
      model: agentConfig.queryGeneratorModel
    });

    const response = await model.generateContent({
      contents: [{ role: "user", parts: [{ text: formattedPrompt }] }],
      tools: [{ googleSearchRetrieval: {} }],
      generationConfig: {
        temperature: 0,
      },
    });

    // Extract grounding metadata and resolve URLs
    const groundingChunks = response.response.candidates?.[0]?.groundingMetadata?.groundingChuncks || [];
    const resolvedUrls = resolveUrls(groundingChunks, state.id);
    
    // Get citations and insert markers
    const citations = getCitations(response.response, resolvedUrls);
    const responseText = response.response.text() || "";
    const modifiedText = insertCitationMarkers(responseText, citations);
    
    // Extract sources from citations
    const sourcesGathered: Source[] = citations.flatMap(citation => 
      citation.segments.map(segment => ({
        label: segment.label,
        shortUrl: segment.shortUrl,
        value: segment.value
      }))
    );
    
    return {
      sourcesGathered,
      searchQuery: [state.searchQuery],
      webResearchResult: [modifiedText],
    };
  } catch (error) {
    console.error("Web research error:", error);
    return {
      sourcesGathered: [],
      searchQuery: [state.searchQuery],
      webResearchResult: [`Error performing web research for: ${state.searchQuery}`],
    };
  }
}

/**
 * Node: Reflect on research results and determine if more research is needed
 * Corresponds to Python reflection function
 */
async function reflection(
  state: typeof OverallState.State,
  config?: RunnableConfig
): Promise<typeof ReflectionState.State> {
  const agentConfig = fromRunnableConfig(config);
  
  // Determine which model to use for reasoning
  const reasoningModel = state.reasoningModel || agentConfig.answerModel;
  
  // Format the prompt for reflection
  const researchTopic = getResearchTopic(state.messages);
  const summaries = state.webResearchResult.join("\n\n");
  const formattedPrompt = formatReflectionPrompt(researchTopic, summaries);
  
  // Initialize reasoning model
  const llm = new ChatGoogleGenerativeAI({
    model: reasoningModel,
    temperature: 1.0,
    maxRetries: 2,
    apiKey: process.env["GEMINI_API_KEY"] || "",
  });
  
  // Create structured output LLM
  const structuredLlm = llm.withStructuredOutput(ReflectionSchema);
  
  // Generate reflection
  const result = await structuredLlm.invoke(formattedPrompt);
  const validatedResult = validateReflection(result);
  
  return {
    isSufficient: validatedResult.is_sufficient,
    knowledgeGap: validatedResult.knowledge_gap,
    followUpQueries: validatedResult.follow_up_queries,
    researchLoopCount: state.researchLoopCount,
    numberOfRanQueries: state.searchQuery.length,
  };
}

/**
 * Conditional edge: Evaluate research and decide next step
 * Corresponds to Python evaluate_research function
 */
function evaluateResearch(
  state: typeof OverallState.State,
  config?: RunnableConfig
): string | Send[] {
  const agentConfig = fromRunnableConfig(config);
  const maxResearchLoops = state.maxResearchLoops ?? agentConfig.maxResearchLoops;

  // For this implementation, we'll use a simple heuristic
  // In a real implementation, this would be based on reflection results
  const currentLoopCount = state.researchLoopCount;
  const hasEnoughResults = state.webResearchResult.length >= 3;

  // Check if research is sufficient or max loops reached
  if (hasEnoughResults || currentLoopCount >= maxResearchLoops) {
    return "finalize_answer";
  } else {
    // For now, we'll just finalize since we don't have follow-up queries in this state
    return "finalize_answer";
  }
}

/**
 * Node: Finalize the research answer
 * Corresponds to Python finalize_answer function
 */
async function finalizeAnswer(
  state: typeof OverallState.State,
  config?: RunnableConfig
): Promise<Partial<typeof OverallState.State>> {
  const agentConfig = fromRunnableConfig(config);
  
  // Deduplicate sources by URL
  const uniqueSources = new Map<string, Source>();
  state.sourcesGathered.forEach(source => {
    if (!uniqueSources.has(source.value)) {
      uniqueSources.set(source.value, source);
    }
  });
  
  const deduplicatedSources = Array.from(uniqueSources.values());
  
  // Combine all research results
  const combinedSummary = state.webResearchResult.join("\n\n");
  
  // Format the final answer prompt
  const researchTopic = getResearchTopic(state.messages);
  const formattedPrompt = formatAnswerPrompt(
    researchTopic,
    combinedSummary
  );

  // Initialize answer model
  const llm = new ChatGoogleGenerativeAI({
    model: agentConfig.answerModel,
    temperature: 0.7,
    maxRetries: 2,
    apiKey: process.env["GEMINI_API_KEY"] || "",
  });
  
  // Generate final answer
  const response = await llm.invoke(formattedPrompt);
  
  return {
    messages: [response],
    sourcesGathered: deduplicatedSources,
  };
}

/**
 * Create and compile the research agent graph
 */
export function createResearchGraph() {
  const workflow = new StateGraph(OverallState)
    .addNode("generate_query", generateQuery)
    .addNode("web_research", webResearch)
    .addNode("reflection", reflection)
    .addNode("finalize_answer", finalizeAnswer)
    .addEdge("__start__", "generate_query")
    .addConditionalEdges("generate_query", continueToWebResearch)
    .addEdge("web_research", "reflection")
    .addConditionalEdges("reflection", evaluateResearch)
    .addEdge("finalize_answer", "__end__");

  return workflow;
}

/**
 * Compile the research graph with default configuration
 */
export function compileResearchGraph() {
  const workflow = createResearchGraph();
  return workflow.compile({ name: "pro-search-agent" });
}

// Export the compiled graph as default
export default compileResearchGraph();
