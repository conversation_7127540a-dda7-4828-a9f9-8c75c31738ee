/**
 * Data validation schemas using Zod
 * 
 * This file defines structured output schemas for LLM responses,
 * corresponding to the Python tools_and_schemas.py file.
 */

import { z } from "zod";

/**
 * Schema for search query list generation
 * Corresponds to Python SearchQueryList
 */
export const SearchQueryListSchema = z.object({
  query: z.array(z.string()).describe("A list of search queries to be used for web research."),
  rationale: z.string().describe("A brief explanation of why these queries are relevant to the research topic."),
});

export type SearchQueryList = z.infer<typeof SearchQueryListSchema>;

/**
 * Schema for reflection on research sufficiency
 * Corresponds to Python Reflection
 */
export const ReflectionSchema = z.object({
  is_sufficient: z.boolean().describe("Whether the provided summaries are sufficient to answer the user's question."),
  knowledge_gap: z.string().describe("A description of what information is missing or needs clarification."),
  follow_up_queries: z.array(z.string()).describe("A list of follow-up queries to address the knowledge gap."),
});

export type Reflection = z.infer<typeof ReflectionSchema>;

/**
 * Schema for individual query with rationale
 * Used internally for query generation
 */
export const QuerySchema = z.object({
  query: z.string().describe("The search query string"),
  rationale: z.string().describe("Explanation for this specific query"),
});

export type Query = z.infer<typeof QuerySchema>;

/**
 * Schema for source citation information
 */
export const SourceSchema = z.object({
  label: z.string().describe("Display label for the source"),
  shortUrl: z.string().describe("Shortened URL for citation"),
  value: z.string().describe("Original URL value"),
});

export type Source = z.infer<typeof SourceSchema>;

/**
 * Schema for citation information from Gemini responses
 */
export const CitationSchema = z.object({
  startIndex: z.number().describe("Starting character index of the cited segment"),
  endIndex: z.number().describe("Ending character index of the cited segment"),
  segments: z.array(SourceSchema).describe("List of source segments for this citation"),
});

export type Citation = z.infer<typeof CitationSchema>;

/**
 * Schema for web search results
 */
export const WebSearchResultSchema = z.object({
  content: z.string().describe("The main content from web search"),
  sources: z.array(SourceSchema).describe("Sources used in the content"),
  searchQuery: z.string().describe("The query that generated this result"),
});

export type WebSearchResult = z.infer<typeof WebSearchResultSchema>;

/**
 * Schema for final answer generation
 */
export const FinalAnswerSchema = z.object({
  answer: z.string().describe("The comprehensive answer to the user's question"),
  sources: z.array(SourceSchema).describe("All sources used in the answer"),
  confidence: z.number().min(0).max(1).describe("Confidence level in the answer"),
});

export type FinalAnswer = z.infer<typeof FinalAnswerSchema>;

/**
 * Validation helper functions
 */
export const validateSearchQueryList = (data: unknown): SearchQueryList => {
  return SearchQueryListSchema.parse(data);
};

export const validateReflection = (data: unknown): Reflection => {
  return ReflectionSchema.parse(data);
};

export const validateQuery = (data: unknown): Query => {
  return QuerySchema.parse(data);
};

export const validateSource = (data: unknown): Source => {
  return SourceSchema.parse(data);
};

export const validateCitation = (data: unknown): Citation => {
  return CitationSchema.parse(data);
};

export const validateWebSearchResult = (data: unknown): WebSearchResult => {
  return WebSearchResultSchema.parse(data);
};

export const validateFinalAnswer = (data: unknown): FinalAnswer => {
  return FinalAnswerSchema.parse(data);
};
