/**
 * Application entry point for the research agent server
 * 
 * This file starts the Express server and handles graceful shutdown,
 * providing the main entry point for the JavaScript backend.
 */

import app from './agent/app.js';
import { getConfig } from './agent/config.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * Server configuration
 */
const PORT = parseInt(process.env['PORT'] || '3001');
const HOST = process.env['HOST'] || '0.0.0.0';
const NODE_ENV = process.env['NODE_ENV'] || 'development';

/**
 * Validate required environment variables
 */
function validateEnvironment(): void {
  const requiredEnvVars = ['GEMINI_API_KEY'];
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.error('❌ Missing required environment variables:');
    missingVars.forEach(varName => {
      console.error(`   - ${varName}`);
    });
    console.error('\nPlease check your .env file or environment configuration.');
    process.exit(1);
  }
}

/**
 * Start the server
 */
async function startServer(): Promise<void> {
  try {
    // Validate environment
    validateEnvironment();
    
    // Get and validate configuration
    const config = getConfig();
    console.log('📋 Configuration loaded:');
    console.log(`   - Query Generator Model: ${config.queryGeneratorModel}`);
    console.log(`   - Reflection Model: ${config.reflectionModel}`);
    console.log(`   - Answer Model: ${config.answerModel}`);
    console.log(`   - Initial Queries: ${config.numberOfInitialQueries}`);
    console.log(`   - Max Research Loops: ${config.maxResearchLoops}`);
    
    // Start the server
    const server = app.listen(PORT, HOST, () => {
      console.log('🚀 Research Agent Server Started');
      console.log(`   - Environment: ${NODE_ENV}`);
      console.log(`   - Server: http://${HOST}:${PORT}`);
      console.log(`   - Health Check: http://${HOST}:${PORT}/health`);
      console.log(`   - Frontend: http://${HOST}:${PORT}/app`);
      console.log(`   - API Config: http://${HOST}:${PORT}/api/config`);
      console.log('');
      console.log('📝 Note: This server provides frontend serving and basic API endpoints.');
      console.log('   For the full LangGraph API, you would typically run this with LangGraph Server.');
      console.log('');
    });

    // Graceful shutdown handling
    const gracefulShutdown = (signal: string) => {
      console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);
      
      server.close((err) => {
        if (err) {
          console.error('❌ Error during server shutdown:', err);
          process.exit(1);
        }
        
        console.log('✅ Server closed successfully');
        console.log('👋 Goodbye!');
        process.exit(0);
      });
      
      // Force shutdown after 10 seconds
      setTimeout(() => {
        console.error('❌ Forced shutdown after timeout');
        process.exit(1);
      }, 10000);
    };

    // Handle shutdown signals
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    
    // Handle uncaught exceptions
    process.on('uncaughtException', (err) => {
      console.error('❌ Uncaught Exception:', err);
      gracefulShutdown('uncaughtException');
    });
    
    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
      gracefulShutdown('unhandledRejection');
    });
    
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

/**
 * Display startup banner
 */
function displayBanner(): void {
  console.log('');
  console.log('╔══════════════════════════════════════════════════════════════╗');
  console.log('║                    Research Agent Server                     ║');
  console.log('║                     JavaScript Backend                       ║');
  console.log('╚══════════════════════════════════════════════════════════════╝');
  console.log('');
}

// Main execution
if (import.meta.url === `file://${process.argv[1]}`) {
  displayBanner();
  startServer().catch((error) => {
    console.error('❌ Startup failed:', error);
    process.exit(1);
  });
}

export { startServer, validateEnvironment };
