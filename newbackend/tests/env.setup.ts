/**
 * Environment setup for tests
 * 
 * This file sets up environment variables for testing.
 */

// Set test environment variables before any imports
process.env.NODE_ENV = 'test';
process.env.GEMINI_API_KEY = 'test-gemini-api-key-12345';
process.env.QUERY_GENERATOR_MODEL = 'test-query-model';
process.env.REFLECTION_MODEL = 'test-reflection-model';
process.env.ANSWER_MODEL = 'test-answer-model';
process.env.NUMBER_OF_INITIAL_QUERIES = '2';
process.env.MAX_RESEARCH_LOOPS = '1';
process.env.PORT = '3001';
process.env.LOG_LEVEL = 'error';
process.env.CORS_ORIGIN = 'http://localhost:3000';

export {};
