/**
 * Unit tests for utility functions
 * 
 * Tests message processing, URL resolution, and citation handling utilities.
 */

import { describe, it, expect } from '@jest/globals';
import {
  getResearchTopic,
  resolveUrls,
  insertCitationMarkers,
  getCitations,
  validateMessages,
  cleanText,
  extractDomain
} from '../../src/agent/utils.ts';
import type { Citation } from '../../src/agent/schemas.ts';

describe('Utility Functions', () => {
  describe('getResearchTopic', () => {
    it('should return content of single message', () => {
      const messages = [global.testUtils.createMockMessage('What is AI?')];
      const result = getResearchTopic(messages);
      expect(result).toBe('What is AI?');
    });

    it('should combine multiple messages with role labels', () => {
      const messages = [
        global.testUtils.createMockMessage('What is machine learning?', 'human'),
        global.testUtils.createMockMessage('Machine learning is...', 'ai'),
        global.testUtils.createMockMessage('Can you explain more?', 'human')
      ];

      const result = getResearchTopic(messages);
      const expected = 'User: What is machine learning?\nAssistant: Machine learning is...\nUser: Can you explain more?';
      expect(result).toBe(expected);
    });

    it('should handle empty messages array', () => {
      const result = getResearchTopic([]);
      expect(result).toBe('');
    });

    it('should handle messages with undefined content', () => {
      const messages = [{ ...global.testUtils.createMockMessage(''), content: undefined }];
      const result = getResearchTopic(messages);
      expect(result).toBe('');
    });

    it('should ignore unknown message types', () => {
      const messages = [
        global.testUtils.createMockMessage('User message', 'human'),
        { ...global.testUtils.createMockMessage('System message'), _getType: () => 'system' },
        global.testUtils.createMockMessage('AI message', 'ai')
      ];

      const result = getResearchTopic(messages);
      expect(result).toBe('User: User message\nAssistant: AI message');
    });
  });

  describe('resolveUrls', () => {
    it('should create shortened URLs for unique URLs', () => {
      const urlsToResolve = [
        { web: { uri: 'https://example.com/long/path/1' } },
        { web: { uri: 'https://example.com/long/path/2' } },
        { web: { uri: 'https://example.com/long/path/3' } }
      ];

      const result = resolveUrls(urlsToResolve, 123);
      
      expect(Object.keys(result)).toHaveLength(3);
      expect(result['https://example.com/long/path/1']).toBe('https://vertexaisearch.cloud.google.com/id/123-0');
      expect(result['https://example.com/long/path/2']).toBe('https://vertexaisearch.cloud.google.com/id/123-1');
      expect(result['https://example.com/long/path/3']).toBe('https://vertexaisearch.cloud.google.com/id/123-2');
    });

    it('should handle duplicate URLs correctly', () => {
      const urlsToResolve = [
        { web: { uri: 'https://example.com/same' } },
        { web: { uri: 'https://example.com/different' } },
        { web: { uri: 'https://example.com/same' } } // Duplicate
      ];

      const result = resolveUrls(urlsToResolve, 456);
      
      expect(Object.keys(result)).toHaveLength(2); // Only unique URLs
      expect(result['https://example.com/same']).toBe('https://vertexaisearch.cloud.google.com/id/456-0');
      expect(result['https://example.com/different']).toBe('https://vertexaisearch.cloud.google.com/id/456-1');
    });

    it('should handle empty array', () => {
      const result = resolveUrls([], 789);
      expect(result).toEqual({});
    });

    it('should handle objects without web.uri', () => {
      const urlsToResolve = [
        { web: { uri: 'https://valid.com' } },
        { web: {} }, // Missing uri
        { other: 'property' }, // Missing web
        { web: { uri: 'https://another-valid.com' } }
      ];

      const result = resolveUrls(urlsToResolve, 999);
      
      expect(Object.keys(result)).toHaveLength(2);
      expect(result['https://valid.com']).toBeDefined();
      expect(result['https://another-valid.com']).toBeDefined();
    });
  });

  describe('insertCitationMarkers', () => {
    it('should insert citation markers at correct positions', () => {
      const text = 'This is a test sentence with citations.';
      const citations: Citation[] = [
        {
          startIndex: 10,
          endIndex: 14, // "test"
          segments: [
            { label: 'Source 1', shortUrl: 'https://short1.com', value: 'https://orig1.com' }
          ]
        }
      ];

      const result = insertCitationMarkers(text, citations);
      expect(result).toBe('This is a test [Source 1](https://short1.com) sentence with citations.');
    });

    it('should handle multiple citations in correct order', () => {
      const text = 'First citation and second citation here.';
      const citations: Citation[] = [
        {
          startIndex: 0,
          endIndex: 5, // "First"
          segments: [
            { label: 'S1', shortUrl: 'https://s1.com', value: 'https://o1.com' }
          ]
        },
        {
          startIndex: 20,
          endIndex: 26, // "second"
          segments: [
            { label: 'S2', shortUrl: 'https://s2.com', value: 'https://o2.com' }
          ]
        }
      ];

      const result = insertCitationMarkers(text, citations);
      expect(result).toBe('First [S1](https://s1.com) citation and second [S2](https://s2.com) citation here.');
    });

    it('should handle multiple segments in one citation', () => {
      const text = 'Text with multiple sources.';
      const citations: Citation[] = [
        {
          startIndex: 10,
          endIndex: 18, // "multiple"
          segments: [
            { label: 'Source A', shortUrl: 'https://a.com', value: 'https://orig-a.com' },
            { label: 'Source B', shortUrl: 'https://b.com', value: 'https://orig-b.com' }
          ]
        }
      ];

      const result = insertCitationMarkers(text, citations);
      expect(result).toBe('Text with multiple [Source A](https://a.com) [Source B](https://b.com) sources.');
    });

    it('should handle empty citations array', () => {
      const text = 'No citations here.';
      const result = insertCitationMarkers(text, []);
      expect(result).toBe(text);
    });

    it('should handle citations with empty segments', () => {
      const text = 'Text with empty citation.';
      const citations: Citation[] = [
        {
          startIndex: 10,
          endIndex: 15,
          segments: []
        }
      ];

      const result = insertCitationMarkers(text, citations);
      expect(result).toBe(text); // No change since no segments
    });
  });

  describe('getCitations', () => {
    it('should extract citations from valid response', () => {
      const response = {
        candidates: [{
          groundingMetadata: {
            groundingSupports: [
              {
                segment: { startIndex: 0, endIndex: 10 },
                groundingChunkIndices: [0]
              }
            ],
            groundingChunks: [
              {
                web: {
                  uri: 'https://example.com',
                  title: 'Example Source.pdf'
                }
              }
            ]
          }
        }]
      };

      const resolvedUrls = {
        'https://example.com': 'https://short.com/1'
      };

      const result = getCitations(response, resolvedUrls);
      
      expect(result).toHaveLength(1);
      expect(result[0].startIndex).toBe(0);
      expect(result[0].endIndex).toBe(10);
      expect(result[0].segments).toHaveLength(1);
      expect(result[0].segments[0].label).toBe('Example Source');
      expect(result[0].segments[0].shortUrl).toBe('https://short.com/1');
    });

    it('should handle response without candidates', () => {
      const response = { candidates: [] };
      const result = getCitations(response, {});
      expect(result).toEqual([]);
    });

    it('should handle response without grounding metadata', () => {
      const response = {
        candidates: [{ someOtherProperty: 'value' }]
      };
      const result = getCitations(response, {});
      expect(result).toEqual([]);
    });

    it('should skip supports without segment info', () => {
      const response = {
        candidates: [{
          groundingMetadata: {
            groundingSupports: [
              { groundingChunkIndices: [0] }, // Missing segment
              {
                segment: { startIndex: 0, endIndex: 10 },
                groundingChunkIndices: [0]
              }
            ],
            groundingChunks: [
              { web: { uri: 'https://example.com', title: 'Test' } }
            ]
          }
        }]
      };

      const result = getCitations(response, { 'https://example.com': 'https://short.com' });
      expect(result).toHaveLength(1); // Only the valid one
    });

    it('should handle missing end index', () => {
      const response = {
        candidates: [{
          groundingMetadata: {
            groundingSupports: [
              {
                segment: { startIndex: 0 }, // Missing endIndex
                groundingChunkIndices: [0]
              }
            ],
            groundingChunks: [
              { web: { uri: 'https://example.com', title: 'Test' } }
            ]
          }
        }]
      };

      const result = getCitations(response, {});
      expect(result).toEqual([]); // Should skip this support
    });
  });

  describe('validateMessages', () => {
    it('should validate correct messages array', () => {
      const messages = [
        global.testUtils.createMockMessage('Message 1'),
        global.testUtils.createMockMessage('Message 2')
      ];

      expect(validateMessages(messages)).toBe(true);
    });

    it('should reject empty array', () => {
      expect(validateMessages([])).toBe(false);
    });

    it('should reject non-array input', () => {
      expect(validateMessages('not an array' as any)).toBe(false);
      expect(validateMessages(null as any)).toBe(false);
      expect(validateMessages(undefined as any)).toBe(false);
    });

    it('should reject messages without required properties', () => {
      const invalidMessages = [
        { content: 'valid' }, // Missing _getType
        { _getType: () => 'human' } // Missing content
      ];

      expect(validateMessages(invalidMessages as any)).toBe(false);
    });
  });

  describe('cleanText', () => {
    it('should normalize whitespace', () => {
      const text = 'Text   with    multiple   spaces';
      const result = cleanText(text);
      expect(result).toBe('Text with multiple spaces');
    });

    it('should normalize line breaks', () => {
      const text = 'Line 1\n\n\n\nLine 2';
      const result = cleanText(text);
      expect(result).toBe('Line 1\n\nLine 2');
    });

    it('should trim whitespace', () => {
      const text = '  \n  Text with padding  \n  ';
      const result = cleanText(text);
      expect(result).toBe('Text with padding');
    });

    it('should handle non-string input', () => {
      expect(cleanText(null as any)).toBe('');
      expect(cleanText(undefined as any)).toBe('');
      expect(cleanText(123 as any)).toBe('');
    });
  });

  describe('extractDomain', () => {
    it('should extract domain from valid URL', () => {
      expect(extractDomain('https://www.example.com/path')).toBe('www.example.com');
      expect(extractDomain('http://subdomain.test.org')).toBe('subdomain.test.org');
      expect(extractDomain('https://localhost:3000')).toBe('localhost');
    });

    it('should handle invalid URLs', () => {
      expect(extractDomain('not-a-url')).toBe('');
      expect(extractDomain('ftp://invalid')).toBe('invalid');
      expect(extractDomain('')).toBe('');
    });

    it('should handle edge cases', () => {
      expect(extractDomain('https://')).toBe('');
      expect(extractDomain('://missing-protocol.com')).toBe('');
    });
  });
});
