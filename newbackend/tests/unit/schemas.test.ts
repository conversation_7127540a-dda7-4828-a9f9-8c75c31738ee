/**
 * Unit tests for Zod schemas
 * 
 * Tests data validation schemas and their validation functions.
 */

import { describe, it, expect } from '@jest/globals';
import { ZodError } from 'zod';
import {
  SearchQueryListSchema,
  ReflectionSchema,
  QuerySchema,
  SourceSchema,
  CitationSchema,
  WebSearchResultSchema,
  FinalAnswerSchema,
  validateSearchQueryList,
  validateReflection,
  validateQuery,
  validateSource,
  validateCitation,
  validateWebSearchResult,
  validateFinalAnswer,
  type SearchQueryList,
  type Reflection,
  type Query,
  type Source,
  type Citation
} from '../../src/agent/schemas.ts';

describe('Schema Validation', () => {
  describe('SearchQueryListSchema', () => {
    it('should validate correct search query list', () => {
      const validData: SearchQueryList = {
        query: ['search term 1', 'search term 2'],
        rationale: 'These queries help find relevant information'
      };

      const result = SearchQueryListSchema.parse(validData);
      expect(result).toEqual(validData);
    });

    it('should reject invalid search query list', () => {
      const invalidData = {
        query: 'not an array', // Should be array
        rationale: 'Valid rationale'
      };

      expect(() => SearchQueryListSchema.parse(invalidData)).toThrow(ZodError);
    });

    it('should reject missing rationale', () => {
      const invalidData = {
        query: ['valid query']
        // Missing rationale
      };

      expect(() => SearchQueryListSchema.parse(invalidData)).toThrow(ZodError);
    });

    it('should reject empty query array', () => {
      const invalidData = {
        query: [], // Empty array
        rationale: 'Valid rationale'
      };

      // Note: Zod doesn't enforce non-empty arrays by default
      const result = SearchQueryListSchema.parse(invalidData);
      expect(result.query).toEqual([]);
    });

    it('should use validation helper function', () => {
      const validData = {
        query: ['test query'],
        rationale: 'test rationale'
      };

      const result = validateSearchQueryList(validData);
      expect(result).toEqual(validData);
    });
  });

  describe('ReflectionSchema', () => {
    it('should validate correct reflection', () => {
      const validData: Reflection = {
        is_sufficient: true,
        knowledge_gap: 'No gaps identified',
        follow_up_queries: []
      };

      const result = ReflectionSchema.parse(validData);
      expect(result).toEqual(validData);
    });

    it('should validate reflection with follow-up queries', () => {
      const validData: Reflection = {
        is_sufficient: false,
        knowledge_gap: 'Need more technical details',
        follow_up_queries: ['What are the technical specifications?', 'How does it compare to alternatives?']
      };

      const result = ReflectionSchema.parse(validData);
      expect(result).toEqual(validData);
    });

    it('should reject invalid boolean for is_sufficient', () => {
      const invalidData = {
        is_sufficient: 'yes', // Should be boolean
        knowledge_gap: 'Valid gap',
        follow_up_queries: []
      };

      expect(() => ReflectionSchema.parse(invalidData)).toThrow(ZodError);
    });

    it('should use validation helper function', () => {
      const validData = {
        is_sufficient: false,
        knowledge_gap: 'test gap',
        follow_up_queries: ['test query']
      };

      const result = validateReflection(validData);
      expect(result).toEqual(validData);
    });
  });

  describe('QuerySchema', () => {
    it('should validate correct query', () => {
      const validData: Query = {
        query: 'test search query',
        rationale: 'this query helps find specific information'
      };

      const result = QuerySchema.parse(validData);
      expect(result).toEqual(validData);
    });

    it('should reject missing query field', () => {
      const invalidData = {
        rationale: 'Valid rationale'
        // Missing query
      };

      expect(() => QuerySchema.parse(invalidData)).toThrow(ZodError);
    });

    it('should use validation helper function', () => {
      const validData = {
        query: 'test query',
        rationale: 'test rationale'
      };

      const result = validateQuery(validData);
      expect(result).toEqual(validData);
    });
  });

  describe('SourceSchema', () => {
    it('should validate correct source', () => {
      const validData: Source = {
        label: 'Example Source',
        shortUrl: 'https://short.url/123',
        value: 'https://original-long-url.com/path/to/resource'
      };

      const result = SourceSchema.parse(validData);
      expect(result).toEqual(validData);
    });

    it('should reject invalid URL format', () => {
      const invalidData = {
        label: 'Valid Label',
        shortUrl: 'not-a-url',
        value: 'https://valid.com'
      };

      // Note: Zod string validation doesn't enforce URL format by default
      const result = SourceSchema.parse(invalidData);
      expect(result.shortUrl).toBe('not-a-url');
    });

    it('should use validation helper function', () => {
      const validData = {
        label: 'Test Source',
        shortUrl: 'https://test.com/short',
        value: 'https://test.com/original'
      };

      const result = validateSource(validData);
      expect(result).toEqual(validData);
    });
  });

  describe('CitationSchema', () => {
    it('should validate correct citation', () => {
      const validData: Citation = {
        startIndex: 0,
        endIndex: 10,
        segments: [
          {
            label: 'Source 1',
            shortUrl: 'https://short1.com',
            value: 'https://original1.com'
          }
        ]
      };

      const result = CitationSchema.parse(validData);
      expect(result).toEqual(validData);
    });

    it('should reject negative indices', () => {
      const invalidData = {
        startIndex: -1, // Should be non-negative
        endIndex: 10,
        segments: []
      };

      // Note: Need to add min(0) constraint to schema for this to fail
      const result = CitationSchema.parse(invalidData);
      expect(result.startIndex).toBe(-1);
    });

    it('should use validation helper function', () => {
      const validData = {
        startIndex: 5,
        endIndex: 15,
        segments: global.testUtils.generateTestSources(1)
      };

      const result = validateCitation(validData);
      expect(result).toEqual(validData);
    });
  });

  describe('WebSearchResultSchema', () => {
    it('should validate correct web search result', () => {
      const validData = {
        content: 'This is the search result content',
        sources: global.testUtils.generateTestSources(2),
        searchQuery: 'original search query'
      };

      const result = WebSearchResultSchema.parse(validData);
      expect(result).toEqual(validData);
    });

    it('should use validation helper function', () => {
      const validData = {
        content: 'test content',
        sources: [],
        searchQuery: 'test query'
      };

      const result = validateWebSearchResult(validData);
      expect(result).toEqual(validData);
    });
  });

  describe('FinalAnswerSchema', () => {
    it('should validate correct final answer', () => {
      const validData = {
        answer: 'This is the comprehensive answer to the question',
        sources: global.testUtils.generateTestSources(3),
        confidence: 0.85
      };

      const result = FinalAnswerSchema.parse(validData);
      expect(result).toEqual(validData);
    });

    it('should reject confidence outside valid range', () => {
      const invalidData = {
        answer: 'Valid answer',
        sources: [],
        confidence: 1.5 // Should be between 0 and 1
      };

      expect(() => FinalAnswerSchema.parse(invalidData)).toThrow(ZodError);
    });

    it('should reject negative confidence', () => {
      const invalidData = {
        answer: 'Valid answer',
        sources: [],
        confidence: -0.1 // Should be between 0 and 1
      };

      expect(() => FinalAnswerSchema.parse(invalidData)).toThrow(ZodError);
    });

    it('should use validation helper function', () => {
      const validData = {
        answer: 'test answer',
        sources: [],
        confidence: 0.9
      };

      const result = validateFinalAnswer(validData);
      expect(result).toEqual(validData);
    });
  });

  describe('Error Handling', () => {
    it('should provide meaningful error messages', () => {
      const invalidData = {
        query: 123, // Should be array
        rationale: null // Should be string
      };

      try {
        SearchQueryListSchema.parse(invalidData);
        fail('Should have thrown ZodError');
      } catch (error) {
        expect(error).toBeInstanceOf(ZodError);
        const zodError = error as ZodError;
        expect(zodError.errors.length).toBeGreaterThan(0);
      }
    });

    it('should handle undefined values', () => {
      expect(() => validateSearchQueryList(undefined)).toThrow(ZodError);
      expect(() => validateReflection(null)).toThrow(ZodError);
      expect(() => validateQuery({})).toThrow(ZodError);
    });
  });
});
