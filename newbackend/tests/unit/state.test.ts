/**
 * Unit tests for state definitions
 * 
 * Tests the LangGraph state definitions and their reducers.
 */

import { describe, it, expect, beforeEach } from '@jest/globals';
import { 
  OverallState, 
  ReflectionState, 
  QueryGenerationState, 
  WebSearchState,
  type Source,
  type Query
} from '../../src/agent/state.ts';

describe('State Definitions', () => {
  describe('OverallState', () => {
    it('should have correct default values', () => {
      const state = OverallState.spec;

      expect(state.messages.initialValueFactory()).toEqual([]);
      expect(state.searchQuery.initialValueFactory()).toEqual([]);
      expect(state.webResearchResult.initialValueFactory()).toEqual([]);
      expect(state.sourcesGathered.initialValueFactory()).toEqual([]);
    });

    it('should correctly reduce messages', () => {
      const initialMessages = [global.testUtils.createMockMessage('Hello')];
      const newMessages = [global.testUtils.createMockMessage('World')];

      const reducer = OverallState.spec.messages.operator;
      const result = reducer(initialMessages, newMessages);

      expect(result).toHaveLength(2);
      expect(result[0].content).toBe('Hello');
      expect(result[1].content).toBe('World');
    });

    it('should correctly reduce search queries', () => {
      const initialQueries = ['query1', 'query2'];
      const newQueries = ['query3', 'query4'];

      const reducer = OverallState.spec.searchQuery.operator;
      const result = reducer(initialQueries, newQueries);

      expect(result).toEqual(['query1', 'query2', 'query3', 'query4']);
    });

    it('should correctly reduce web research results', () => {
      const initialResults = ['result1'];
      const newResults = ['result2', 'result3'];

      const reducer = OverallState.spec.webResearchResult.operator;
      const result = reducer(initialResults, newResults);

      expect(result).toEqual(['result1', 'result2', 'result3']);
    });

    it('should correctly reduce sources gathered', () => {
      const initialSources: Source[] = [{
        label: 'Source 1',
        shortUrl: 'https://short1.com',
        value: 'https://original1.com'
      }];

      const newSources: Source[] = [{
        label: 'Source 2',
        shortUrl: 'https://short2.com',
        value: 'https://original2.com'
      }];

      const reducer = OverallState.spec.sourcesGathered.operator;
      const result = reducer(initialSources, newSources);

      expect(result).toHaveLength(2);
      expect(result[0].label).toBe('Source 1');
      expect(result[1].label).toBe('Source 2');
    });
  });

  describe('ReflectionState', () => {
    it('should have correct structure', () => {
      const state = ReflectionState.spec;
      
      expect(state.isSufficient).toBeDefined();
      expect(state.knowledgeGap).toBeDefined();
      expect(state.followUpQueries).toBeDefined();
      expect(state.researchLoopCount).toBeDefined();
      expect(state.numberOfRanQueries).toBeDefined();
    });

    it('should correctly reduce follow-up queries', () => {
      const initialQueries = ['query1'];
      const newQueries = ['query2', 'query3'];

      const reducer = ReflectionState.spec.followUpQueries.operator;
      const result = reducer(initialQueries, newQueries);

      expect(result).toEqual(['query1', 'query2', 'query3']);
    });

    it('should have default empty array for follow-up queries', () => {
      const defaultValue = ReflectionState.spec.followUpQueries.initialValueFactory();
      expect(defaultValue).toEqual([]);
    });
  });

  describe('QueryGenerationState', () => {
    it('should have correct structure', () => {
      const state = QueryGenerationState.spec;
      expect(state.queryList).toBeDefined();
    });

    it('should handle query list correctly', () => {
      const queries: Query[] = [
        { query: 'test query 1', rationale: 'test rationale 1' },
        { query: 'test query 2', rationale: 'test rationale 2' }
      ];
      
      // Since queryList doesn't have a reducer, it should be a simple annotation
      expect(QueryGenerationState.spec.queryList).toBeDefined();
    });
  });

  describe('WebSearchState', () => {
    it('should have correct structure', () => {
      const state = WebSearchState.spec;
      
      expect(state.searchQuery).toBeDefined();
      expect(state.id).toBeDefined();
    });

    it('should handle search query and id', () => {
      // Test that the state can hold search query and id
      const testQuery = 'test search query';
      const testId = 123;
      
      expect(typeof testQuery).toBe('string');
      expect(typeof testId).toBe('number');
    });
  });

  describe('Type Safety', () => {
    it('should enforce correct types for OverallState', () => {
      const mockState = global.testUtils.createMockState();
      
      expect(Array.isArray(mockState.messages)).toBe(true);
      expect(Array.isArray(mockState.searchQuery)).toBe(true);
      expect(Array.isArray(mockState.webResearchResult)).toBe(true);
      expect(Array.isArray(mockState.sourcesGathered)).toBe(true);
      expect(typeof mockState.initialSearchQueryCount).toBe('number');
      expect(typeof mockState.maxResearchLoops).toBe('number');
      expect(typeof mockState.researchLoopCount).toBe('number');
      expect(typeof mockState.reasoningModel).toBe('string');
    });

    it('should handle empty state correctly', () => {
      const emptyState = {
        messages: [],
        searchQuery: [],
        webResearchResult: [],
        sourcesGathered: [],
        initialSearchQueryCount: 0,
        maxResearchLoops: 0,
        researchLoopCount: 0,
        reasoningModel: ''
      };
      
      expect(emptyState).toBeValidState();
    });
  });

  describe('State Transitions', () => {
    it('should maintain state consistency during updates', () => {
      const initialState = global.testUtils.createMockState({
        searchQuery: ['initial query'],
        researchLoopCount: 0
      });

      // Simulate adding new search queries
      const newQueries = ['new query 1', 'new query 2'];
      const reducer = OverallState.spec.searchQuery.operator;
      const updatedQueries = reducer(initialState.searchQuery, newQueries);

      expect(updatedQueries).toHaveLength(3);
      expect(updatedQueries).toContain('initial query');
      expect(updatedQueries).toContain('new query 1');
      expect(updatedQueries).toContain('new query 2');
    });

    it('should handle concurrent state updates', () => {
      const sources1: Source[] = [{ label: 'S1', shortUrl: 'url1', value: 'val1' }];
      const sources2: Source[] = [{ label: 'S2', shortUrl: 'url2', value: 'val2' }];

      const reducer = OverallState.spec.sourcesGathered.operator;

      // Simulate concurrent updates
      const result1 = reducer([], sources1);
      const result2 = reducer(result1, sources2);

      expect(result2).toHaveLength(2);
      expect(result2[0].label).toBe('S1');
      expect(result2[1].label).toBe('S2');
    });
  });
});
