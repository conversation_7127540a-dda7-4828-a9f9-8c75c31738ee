/**
 * Unit tests for configuration management
 * 
 * Tests configuration loading, validation, and environment variable handling.
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import {
  getConfig,
  fromRunnableConfig,
  validateConfig,
  getValidatedConfig,
  defaultConfig,
  configMetadata,
  type AgentConfig
} from '../../src/agent/config.ts';

describe('Configuration Management', () => {
  let originalEnv: NodeJS.ProcessEnv;

  beforeEach(() => {
    // Save original environment
    originalEnv = { ...process.env };
  });

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv;
  });

  describe('getConfig', () => {
    it('should return default configuration when no overrides', () => {
      // Clear environment variables
      delete process.env.QUERY_GENERATOR_MODEL;
      delete process.env.REFLECTION_MODEL;
      delete process.env.ANSWER_MODEL;
      delete process.env.NUMBER_OF_INITIAL_QUERIES;
      delete process.env.MAX_RESEARCH_LOOPS;

      const config = getConfig();
      expect(config).toEqual(defaultConfig);
    });

    it('should use environment variables when available', () => {
      process.env.QUERY_GENERATOR_MODEL = 'env-query-model';
      process.env.REFLECTION_MODEL = 'env-reflection-model';
      process.env.ANSWER_MODEL = 'env-answer-model';
      process.env.NUMBER_OF_INITIAL_QUERIES = '5';
      process.env.MAX_RESEARCH_LOOPS = '3';

      const config = getConfig();
      
      expect(config.queryGeneratorModel).toBe('env-query-model');
      expect(config.reflectionModel).toBe('env-reflection-model');
      expect(config.answerModel).toBe('env-answer-model');
      expect(config.numberOfInitialQueries).toBe(5);
      expect(config.maxResearchLoops).toBe(3);
    });

    it('should apply overrides correctly', () => {
      // Clear environment variable to test default behavior
      const originalReflectionModel = process.env.REFLECTION_MODEL;
      delete process.env.REFLECTION_MODEL;

      const overrides: Partial<AgentConfig> = {
        queryGeneratorModel: 'override-model',
        numberOfInitialQueries: 7
      };

      const config = getConfig(overrides);

      expect(config.queryGeneratorModel).toBe('override-model');
      expect(config.numberOfInitialQueries).toBe(7);
      expect(config.reflectionModel).toBe(defaultConfig.reflectionModel);

      // Restore environment variable
      if (originalReflectionModel) {
        process.env.REFLECTION_MODEL = originalReflectionModel;
      }
    });

    it('should prioritize overrides over environment variables', () => {
      process.env.QUERY_GENERATOR_MODEL = 'env-model';
      
      const overrides: Partial<AgentConfig> = {
        queryGeneratorModel: 'override-model'
      };

      const config = getConfig(overrides);
      expect(config.queryGeneratorModel).toBe('override-model');
    });

    it('should handle invalid number strings in environment', () => {
      process.env.NUMBER_OF_INITIAL_QUERIES = 'not-a-number';
      process.env.MAX_RESEARCH_LOOPS = 'also-not-a-number';

      const config = getConfig();
      
      // parseInt of invalid string returns NaN
      expect(config.numberOfInitialQueries).toBeNaN();
      expect(config.maxResearchLoops).toBeNaN();
    });

    it('should be a valid config structure', () => {
      const config = getConfig();
      expect(config).toBeValidConfig();
    });
  });

  describe('fromRunnableConfig', () => {
    it('should handle undefined config', () => {
      const config = fromRunnableConfig();
      expect(config).toEqual(getConfig());
    });

    it('should handle empty config', () => {
      const config = fromRunnableConfig({});
      expect(config).toEqual(getConfig());
    });

    it('should extract values from configurable object', () => {
      const runnableConfig = {
        configurable: {
          queryGeneratorModel: 'runnable-query-model',
          reflectionModel: 'runnable-reflection-model',
          numberOfInitialQueries: '4',
          maxResearchLoops: '2'
        }
      };

      const config = fromRunnableConfig(runnableConfig);
      
      expect(config.queryGeneratorModel).toBe('runnable-query-model');
      expect(config.reflectionModel).toBe('runnable-reflection-model');
      expect(config.numberOfInitialQueries).toBe(4);
      expect(config.maxResearchLoops).toBe(2);
    });

    it('should handle partial configurable object', () => {
      // Clear environment variable to test default behavior
      const originalReflectionModel = process.env.REFLECTION_MODEL;
      delete process.env.REFLECTION_MODEL;

      const runnableConfig = {
        configurable: {
          queryGeneratorModel: 'partial-model'
          // Other fields missing
        }
      };

      const config = fromRunnableConfig(runnableConfig);

      expect(config.queryGeneratorModel).toBe('partial-model');
      expect(config.reflectionModel).toBe(defaultConfig.reflectionModel);

      // Restore environment variable
      if (originalReflectionModel) {
        process.env.REFLECTION_MODEL = originalReflectionModel;
      }
    });

    it('should handle config without configurable property', () => {
      const runnableConfig = {
        someOtherProperty: 'value'
      };

      const config = fromRunnableConfig(runnableConfig);
      expect(config).toEqual(getConfig());
    });
  });

  describe('validateConfig', () => {
    it('should pass validation for valid config', () => {
      const validConfig: AgentConfig = {
        queryGeneratorModel: 'valid-model',
        reflectionModel: 'valid-reflection',
        answerModel: 'valid-answer',
        numberOfInitialQueries: 3,
        maxResearchLoops: 2
      };

      expect(() => validateConfig(validConfig)).not.toThrow();
    });

    it('should reject empty model names', () => {
      const invalidConfig: AgentConfig = {
        ...defaultConfig,
        queryGeneratorModel: ''
      };

      expect(() => validateConfig(invalidConfig)).toThrow('Query generator model cannot be empty');
    });

    it('should reject whitespace-only model names', () => {
      const invalidConfig: AgentConfig = {
        ...defaultConfig,
        reflectionModel: '   '
      };

      expect(() => validateConfig(invalidConfig)).toThrow('Reflection model cannot be empty');
    });

    it('should reject invalid number of initial queries', () => {
      const invalidConfig: AgentConfig = {
        ...defaultConfig,
        numberOfInitialQueries: 0
      };

      expect(() => validateConfig(invalidConfig)).toThrow('Number of initial queries must be between 1 and 10');
    });

    it('should reject too many initial queries', () => {
      const invalidConfig: AgentConfig = {
        ...defaultConfig,
        numberOfInitialQueries: 15
      };

      expect(() => validateConfig(invalidConfig)).toThrow('Number of initial queries must be between 1 and 10');
    });

    it('should reject invalid max research loops', () => {
      const invalidConfig: AgentConfig = {
        ...defaultConfig,
        maxResearchLoops: 0
      };

      expect(() => validateConfig(invalidConfig)).toThrow('Max research loops must be between 1 and 5');
    });

    it('should reject too many research loops', () => {
      const invalidConfig: AgentConfig = {
        ...defaultConfig,
        maxResearchLoops: 10
      };

      expect(() => validateConfig(invalidConfig)).toThrow('Max research loops must be between 1 and 5');
    });
  });

  describe('getValidatedConfig', () => {
    it('should return validated config for valid input', () => {
      const config = getValidatedConfig();
      expect(config).toBeValidConfig();
    });

    it('should throw for invalid configuration', () => {
      process.env.NUMBER_OF_INITIAL_QUERIES = '0'; // Invalid value

      expect(() => getValidatedConfig()).toThrow();
    });

    it('should apply overrides and validate', () => {
      const overrides: Partial<AgentConfig> = {
        numberOfInitialQueries: 5
      };

      const config = getValidatedConfig(overrides);
      expect(config.numberOfInitialQueries).toBe(5);
    });
  });

  describe('defaultConfig', () => {
    it('should have all required fields', () => {
      expect(defaultConfig).toBeValidConfig();
    });

    it('should have reasonable default values', () => {
      expect(defaultConfig.numberOfInitialQueries).toBeGreaterThan(0);
      expect(defaultConfig.numberOfInitialQueries).toBeLessThanOrEqual(10);
      expect(defaultConfig.maxResearchLoops).toBeGreaterThan(0);
      expect(defaultConfig.maxResearchLoops).toBeLessThanOrEqual(5);
    });

    it('should have non-empty model names', () => {
      expect(defaultConfig.queryGeneratorModel.length).toBeGreaterThan(0);
      expect(defaultConfig.reflectionModel.length).toBeGreaterThan(0);
      expect(defaultConfig.answerModel.length).toBeGreaterThan(0);
    });
  });

  describe('configMetadata', () => {
    it('should have metadata for all config fields', () => {
      const configFields = Object.keys(defaultConfig);
      const metadataFields = Object.keys(configMetadata);
      
      expect(metadataFields.sort()).toEqual(configFields.sort());
    });

    it('should have complete metadata for each field', () => {
      Object.entries(configMetadata).forEach(([field, metadata]) => {
        expect(metadata.description).toBeDefined();
        expect(metadata.description.length).toBeGreaterThan(0);
        expect(metadata.default).toBeDefined();
        expect(metadata.envVar).toBeDefined();
        expect(metadata.envVar.length).toBeGreaterThan(0);
      });
    });

    it('should have correct default values in metadata', () => {
      expect(configMetadata.queryGeneratorModel.default).toBe(defaultConfig.queryGeneratorModel);
      expect(configMetadata.reflectionModel.default).toBe(defaultConfig.reflectionModel);
      expect(configMetadata.answerModel.default).toBe(defaultConfig.answerModel);
      expect(configMetadata.numberOfInitialQueries.default).toBe(defaultConfig.numberOfInitialQueries);
      expect(configMetadata.maxResearchLoops.default).toBe(defaultConfig.maxResearchLoops);
    });
  });
});
