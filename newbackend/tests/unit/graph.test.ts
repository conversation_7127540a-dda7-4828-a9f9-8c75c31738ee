/**
 * Unit tests for graph.ts - LangGraph state graph implementation
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { createResearchGraph, compileResearchGraph } from '../../src/agent/graph.ts';
import { OverallState } from '../../src/agent/state.ts';
import { testMessages, testStates } from '../fixtures/test-data.ts';

// Mock the Google Generative AI client
jest.mock('@google/generative-ai', () => ({
  GoogleGenerativeAI: jest.fn().mockImplementation(() => ({
    getGenerativeModel: jest.fn().mockReturnValue({
      generateContent: jest.fn().mockResolvedValue({
        response: {
          text: jest.fn().mockReturnValue('Mock response text'),
          candidates: [{
            groundingMetadata: {
              groundingChuncks: []
            }
          }]
        }
      })
    })
  }))
}));

// Mock the ChatGoogleGenerativeAI
jest.mock('@langchain/google-genai', () => ({
  ChatGoogleGenerativeAI: jest.fn().mockImplementation(() => ({
    withStructuredOutput: jest.fn().mockReturnValue({
      invoke: jest.fn().mockResolvedValue({
        query: ['test query 1', 'test query 2'],
        rationale: 'Test rationale'
      })
    }),
    invoke: jest.fn().mockResolvedValue({
      content: 'Mock LLM response'
    })
  }))
}));

describe('Graph Implementation', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Set up environment variables for tests
    process.env['GEMINI_API_KEY'] = 'test-api-key';
  });

  describe('createResearchGraph', () => {
    it('should create a state graph with correct structure', () => {
      const graph = createResearchGraph();
      
      expect(graph).toBeDefined();
      expect(typeof graph.compile).toBe('function');
    });

    it('should have all required nodes', () => {
      const graph = createResearchGraph();
      
      // The graph should be created without errors
      expect(graph).toBeDefined();
      
      // We can't easily inspect the internal nodes without running the graph,
      // but we can verify the graph compiles successfully
      expect(() => graph.compile()).not.toThrow();
    });
  });

  describe('compileResearchGraph', () => {
    it('should compile the research graph successfully', () => {
      const compiledGraph = compileResearchGraph();
      
      expect(compiledGraph).toBeDefined();
      expect(typeof compiledGraph.invoke).toBe('function');
    });

    it('should create a graph with the correct name', () => {
      const compiledGraph = compileResearchGraph();
      
      // The compiled graph should have the expected structure
      expect(compiledGraph).toBeDefined();
    });
  });

  describe('Graph State Management', () => {
    it('should handle initial state correctly', () => {
      // Use the test state from fixtures
      const initialState = testStates.initial;

      // Verify the state structure matches OverallState
      expect(initialState).toMatchObject({
        messages: expect.any(Array),
        searchQuery: expect.any(Array),
        webResearchResult: expect.any(Array),
        sourcesGathered: expect.any(Array),
        researchLoopCount: expect.any(Number),
        maxResearchLoops: expect.any(Number),
        initialSearchQueryCount: expect.any(Number)
      });
    });

    it('should handle state transitions', () => {
      const state1 = testStates.initial;
      const state2 = testStates.withResults;

      // Verify state evolution
      expect(state2.webResearchResult.length).toBeGreaterThan(state1.webResearchResult.length);
      expect(state2.sourcesGathered.length).toBeGreaterThan(state1.sourcesGathered.length);
    });
  });

  describe('Error Handling', () => {
    it('should handle missing API key gracefully', () => {
      delete process.env['GEMINI_API_KEY'];
      
      // The graph should still be created, but API calls would fail
      expect(() => createResearchGraph()).not.toThrow();
      expect(() => compileResearchGraph()).not.toThrow();
    });

    it('should handle invalid configuration', () => {
      // Test with invalid config
      const graph = createResearchGraph();
      expect(graph).toBeDefined();
    });
  });

  describe('Integration Points', () => {
    it('should integrate with state definitions', () => {
      // Verify that the graph works with our state definitions
      const graph = createResearchGraph();
      const compiled = graph.compile();
      
      expect(compiled).toBeDefined();
    });

    it('should integrate with configuration system', () => {
      // Test that the graph respects configuration
      const graph = createResearchGraph();
      expect(graph).toBeDefined();
    });
  });

  describe('Node Function Coverage', () => {
    it('should have generateQuery function available', () => {
      // This is an indirect test since the functions are not exported
      const graph = createResearchGraph();
      expect(graph).toBeDefined();
    });

    it('should have webResearch function available', () => {
      const graph = createResearchGraph();
      expect(graph).toBeDefined();
    });

    it('should have reflection function available', () => {
      const graph = createResearchGraph();
      expect(graph).toBeDefined();
    });

    it('should have finalizeAnswer function available', () => {
      const graph = createResearchGraph();
      expect(graph).toBeDefined();
    });
  });

  describe('Conditional Edges', () => {
    it('should have continueToWebResearch edge function', () => {
      const graph = createResearchGraph();
      expect(graph).toBeDefined();
    });

    it('should have evaluateResearch edge function', () => {
      const graph = createResearchGraph();
      expect(graph).toBeDefined();
    });
  });

  describe('Graph Compilation', () => {
    it('should compile without errors', () => {
      expect(() => {
        const graph = createResearchGraph();
        const compiled = graph.compile({ name: "test-graph" });
        return compiled;
      }).not.toThrow();
    });

    it('should create a runnable graph', () => {
      const compiled = compileResearchGraph();
      
      // The compiled graph should have invoke method
      expect(typeof compiled.invoke).toBe('function');
    });
  });

  describe('Default Export', () => {
    it('should export a compiled graph by default', async () => {
      const { default: defaultGraph } = await import('../../src/agent/graph.ts');

      expect(defaultGraph).toBeDefined();
      expect(typeof defaultGraph.invoke).toBe('function');
    });
  });
});
