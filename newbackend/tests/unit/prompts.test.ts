/**
 * Unit tests for prompt templates
 * 
 * Tests prompt generation, formatting, and template variable replacement.
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import {
  getCurrentDate,
  queryWriterInstructions,
  webSearcherInstructions,
  reflectionInstructions,
  answerInstructions,
  formatQueryWriterPrompt,
  formatWebSearcherPrompt,
  formatReflectionPrompt,
  formatAnswerPrompt,
  validatePromptVariables,
  replaceTemplateVariables
} from '../../src/agent/prompts.ts';

describe('Prompt Templates', () => {
  describe('getCurrentDate', () => {
    it('should return current date in correct format', () => {
      const result = getCurrentDate();
      
      // Should match format like "January 15, 2024"
      const datePattern = /^[A-Z][a-z]+ \d{1,2}, \d{4}$/;
      expect(result).toMatch(datePattern);
    });

    it('should return consistent format', () => {
      const date1 = getCurrentDate();
      const date2 = getCurrentDate();
      
      // Should have same format (might be different if called across midnight)
      expect(typeof date1).toBe('string');
      expect(typeof date2).toBe('string');
    });

    it('should handle date formatting correctly', () => {
      // Mock a specific date to test formatting
      const mockDate = new Date('2024-01-15T10:30:00Z');
      const originalDate = Date;
      
      global.Date = jest.fn(() => mockDate) as any;
      global.Date.prototype = originalDate.prototype;
      
      // Mock toLocaleDateString
      mockDate.toLocaleDateString = jest.fn().mockReturnValue('January 15, 2024');
      
      const result = getCurrentDate();
      expect(result).toBe('January 15, 2024');
      
      // Restore original Date
      global.Date = originalDate;
    });
  });

  describe('Template Constants', () => {
    it('should have all required template variables in queryWriterInstructions', () => {
      expect(queryWriterInstructions).toContain('{research_topic}');
      expect(queryWriterInstructions).toContain('{number_queries}');
      expect(queryWriterInstructions).toContain('{current_date}');
    });

    it('should have required template variables in webSearcherInstructions', () => {
      expect(webSearcherInstructions).toContain('{research_topic}');
      expect(webSearcherInstructions).toContain('{current_date}');
    });

    it('should have required template variables in reflectionInstructions', () => {
      expect(reflectionInstructions).toContain('{research_topic}');
      expect(reflectionInstructions).toContain('{summaries}');
    });

    it('should have required template variables in answerInstructions', () => {
      expect(answerInstructions).toContain('{research_topic}');
      expect(answerInstructions).toContain('{summaries}');
      expect(answerInstructions).toContain('{current_date}');
    });

    it('should contain proper JSON format examples', () => {
      expect(queryWriterInstructions).toContain('```json');
      expect(reflectionInstructions).toContain('```json');
    });

    it('should have clear instructions', () => {
      expect(queryWriterInstructions.length).toBeGreaterThan(100);
      expect(webSearcherInstructions.length).toBeGreaterThan(50);
      expect(reflectionInstructions.length).toBeGreaterThan(100);
      expect(answerInstructions.length).toBeGreaterThan(50);
    });
  });

  describe('formatQueryWriterPrompt', () => {
    it('should replace all template variables', () => {
      const researchTopic = 'What is artificial intelligence?';
      const numberQueries = 3;
      
      const result = formatQueryWriterPrompt(researchTopic, numberQueries);
      
      expect(result).toContain(researchTopic);
      expect(result).toContain('3');
      expect(result).not.toContain('{research_topic}');
      expect(result).not.toContain('{number_queries}');
      expect(result).not.toContain('{current_date}');
    });

    it('should include current date', () => {
      const result = formatQueryWriterPrompt('test topic', 2);
      const currentDate = getCurrentDate();
      
      expect(result).toContain(currentDate);
    });

    it('should handle special characters in research topic', () => {
      const researchTopic = 'What is "machine learning" & how does it work?';
      const result = formatQueryWriterPrompt(researchTopic, 1);
      
      expect(result).toContain(researchTopic);
    });

    it('should handle different number of queries', () => {
      const result1 = formatQueryWriterPrompt('topic', 1);
      const result5 = formatQueryWriterPrompt('topic', 5);
      
      expect(result1).toContain('1');
      expect(result5).toContain('5');
    });
  });

  describe('formatWebSearcherPrompt', () => {
    it('should replace template variables', () => {
      const researchTopic = 'Climate change impacts';
      const result = formatWebSearcherPrompt(researchTopic);
      
      expect(result).toContain(researchTopic);
      expect(result).toContain(getCurrentDate());
      expect(result).not.toContain('{research_topic}');
      expect(result).not.toContain('{current_date}');
    });

    it('should handle empty research topic', () => {
      const result = formatWebSearcherPrompt('');
      expect(result).not.toContain('{research_topic}');
    });

    it('should maintain instruction structure', () => {
      const result = formatWebSearcherPrompt('test');
      expect(result).toContain('Instructions:');
      expect(result).toContain('Research Topic:');
    });
  });

  describe('formatReflectionPrompt', () => {
    it('should replace template variables', () => {
      const researchTopic = 'Quantum computing';
      const summaries = 'Summary 1: Quantum computers use qubits...';
      
      const result = formatReflectionPrompt(researchTopic, summaries);
      
      expect(result).toContain(researchTopic);
      expect(result).toContain(summaries);
      expect(result).not.toContain('{research_topic}');
      expect(result).not.toContain('{summaries}');
    });

    it('should handle multiple summaries', () => {
      const summaries = 'Summary 1: First point.\nSummary 2: Second point.';
      const result = formatReflectionPrompt('topic', summaries);
      
      expect(result).toContain('Summary 1');
      expect(result).toContain('Summary 2');
    });

    it('should maintain JSON format example', () => {
      const result = formatReflectionPrompt('topic', 'summaries');
      expect(result).toContain('```json');
      expect(result).toContain('"is_sufficient"');
      expect(result).toContain('"knowledge_gap"');
      expect(result).toContain('"follow_up_queries"');
    });
  });

  describe('formatAnswerPrompt', () => {
    it('should replace all template variables', () => {
      const researchTopic = 'Renewable energy';
      const summaries = 'Solar power summary...';
      
      const result = formatAnswerPrompt(researchTopic, summaries);
      
      expect(result).toContain(researchTopic);
      expect(result).toContain(summaries);
      expect(result).toContain(getCurrentDate());
      expect(result).not.toContain('{research_topic}');
      expect(result).not.toContain('{summaries}');
      expect(result).not.toContain('{current_date}');
    });

    it('should include citation requirements', () => {
      const result = formatAnswerPrompt('topic', 'summaries');
      expect(result.toLowerCase()).toContain('citation');
    });

    it('should maintain instruction structure', () => {
      const result = formatAnswerPrompt('topic', 'summaries');
      expect(result).toContain('Instructions:');
      expect(result).toContain('User Context:');
      expect(result).toContain('Summaries:');
    });
  });

  describe('validatePromptVariables', () => {
    it('should return true when all variables are present', () => {
      const template = 'Hello {name}, today is {date}';
      const variables = { name: 'John', date: '2024-01-15' };
      
      expect(validatePromptVariables(template, variables)).toBe(true);
    });

    it('should return false when variables are missing', () => {
      const template = 'Hello {name}, today is {date}';
      const variables = { name: 'John' }; // Missing date
      
      expect(validatePromptVariables(template, variables)).toBe(false);
    });

    it('should return true for template without variables', () => {
      const template = 'No variables here';
      const variables = {};
      
      expect(validatePromptVariables(template, variables)).toBe(true);
    });

    it('should handle extra variables gracefully', () => {
      const template = 'Hello {name}';
      const variables = { name: 'John', extra: 'value' };
      
      expect(validatePromptVariables(template, variables)).toBe(true);
    });

    it('should handle undefined and null values', () => {
      const template = 'Hello {name}';
      
      expect(validatePromptVariables(template, { name: undefined })).toBe(false);
      expect(validatePromptVariables(template, { name: null })).toBe(false);
      expect(validatePromptVariables(template, { name: '' })).toBe(true); // Empty string is valid
    });
  });

  describe('replaceTemplateVariables', () => {
    it('should replace all occurrences of variables', () => {
      const template = 'Hello {name}, {name} is great!';
      const variables = { name: 'Alice' };
      
      const result = replaceTemplateVariables(template, variables);
      expect(result).toBe('Hello Alice, Alice is great!');
    });

    it('should handle multiple different variables', () => {
      const template = '{greeting} {name}, today is {day}';
      const variables = { greeting: 'Hi', name: 'Bob', day: 'Monday' };
      
      const result = replaceTemplateVariables(template, variables);
      expect(result).toBe('Hi Bob, today is Monday');
    });

    it('should leave unreplaced variables as-is', () => {
      const template = 'Hello {name}, see you {when}';
      const variables = { name: 'Charlie' };
      
      const result = replaceTemplateVariables(template, variables);
      expect(result).toBe('Hello Charlie, see you {when}');
    });

    it('should handle special characters in variables', () => {
      const template = 'Query: {query}';
      const variables = { query: 'What is "AI" & ML?' };
      
      const result = replaceTemplateVariables(template, variables);
      expect(result).toBe('Query: What is "AI" & ML?');
    });

    it('should convert non-string values to strings', () => {
      const template = 'Count: {count}, Active: {active}';
      const variables = { count: 42, active: true };
      
      const result = replaceTemplateVariables(template, variables);
      expect(result).toBe('Count: 42, Active: true');
    });

    it('should handle empty template', () => {
      const result = replaceTemplateVariables('', { name: 'test' });
      expect(result).toBe('');
    });

    it('should handle empty variables object', () => {
      const template = 'No variables to replace';
      const result = replaceTemplateVariables(template, {});
      expect(result).toBe(template);
    });
  });

  describe('Integration Tests', () => {
    it('should format complete query writer prompt correctly', () => {
      const topic = 'Machine learning applications in healthcare';
      const queries = 4;
      
      const result = formatQueryWriterPrompt(topic, queries);
      
      // Should be valid and complete
      expect(result.length).toBeGreaterThan(500);
      expect(validatePromptVariables(result, {})).toBe(true); // No unreplaced variables
    });

    it('should format complete reflection prompt correctly', () => {
      const topic = 'Blockchain technology';
      const summaries = 'Summary 1: Blockchain is...\nSummary 2: Applications include...';
      
      const result = formatReflectionPrompt(topic, summaries);
      
      expect(result.length).toBeGreaterThan(300);
      expect(validatePromptVariables(result, {})).toBe(true);
    });

    it('should handle real-world research topics', () => {
      const complexTopic = 'How do large language models like GPT-4 handle multi-modal inputs and what are the implications for AI safety?';
      
      const queryPrompt = formatQueryWriterPrompt(complexTopic, 3);
      const searchPrompt = formatWebSearcherPrompt(complexTopic);
      
      expect(queryPrompt).toContain(complexTopic);
      expect(searchPrompt).toContain(complexTopic);
      expect(queryPrompt.length).toBeGreaterThan(complexTopic.length);
      expect(searchPrompt.length).toBeGreaterThan(complexTopic.length);
    });
  });
});
