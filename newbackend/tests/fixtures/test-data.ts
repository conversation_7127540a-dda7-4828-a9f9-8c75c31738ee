/**
 * Test data fixtures
 * 
 * Provides reusable test data for unit and integration tests.
 */

import type { 
  SearchQueryList, 
  Reflection, 
  Query, 
  Source, 
  Citation,
  WebSearchResult,
  FinalAnswer 
} from '../../src/agent/schemas.js';
import type { AgentConfig } from '../../src/agent/config.js';

// Sample configurations
export const testConfigs = {
  default: {
    queryGeneratorModel: 'test-query-model',
    reflectionModel: 'test-reflection-model',
    answerModel: 'test-answer-model',
    numberOfInitialQueries: 3,
    maxResearchLoops: 2
  } as AgentConfig,

  minimal: {
    queryGeneratorModel: 'minimal-model',
    reflectionModel: 'minimal-model',
    answerModel: 'minimal-model',
    numberOfInitialQueries: 1,
    maxResearchLoops: 1
  } as AgentConfig,

  extensive: {
    queryGeneratorModel: 'extensive-query-model',
    reflectionModel: 'extensive-reflection-model',
    answerModel: 'extensive-answer-model',
    numberOfInitialQueries: 5,
    maxResearchLoops: 3
  } as AgentConfig
};

// Sample research topics
export const testTopics = {
  simple: 'What is artificial intelligence?',
  complex: 'How do transformer neural networks handle attention mechanisms and what are the computational complexity implications for large language models?',
  multiPart: 'What are the benefits and risks of renewable energy adoption, and how do different countries approach energy transition policies?',
  technical: 'Explain the differences between supervised, unsupervised, and reinforcement learning algorithms with practical examples.',
  current: 'What are the latest developments in quantum computing and their potential impact on cryptography?'
};

// Sample search queries
export const testSearchQueries: SearchQueryList[] = [
  {
    query: ['artificial intelligence definition', 'AI applications 2024'],
    rationale: 'Basic queries to understand AI fundamentals and current applications'
  },
  {
    query: [
      'transformer neural networks attention mechanism',
      'computational complexity large language models',
      'transformer architecture efficiency'
    ],
    rationale: 'Technical queries focusing on transformer architecture and computational aspects'
  },
  {
    query: ['renewable energy benefits risks', 'energy transition policies by country'],
    rationale: 'Comprehensive queries covering both advantages/disadvantages and policy approaches'
  }
];

// Sample reflections
export const testReflections: Reflection[] = [
  {
    is_sufficient: true,
    knowledge_gap: '',
    follow_up_queries: []
  },
  {
    is_sufficient: false,
    knowledge_gap: 'Missing information about recent developments and practical implementations',
    follow_up_queries: [
      'Latest AI breakthroughs 2024',
      'Real-world AI implementation case studies'
    ]
  },
  {
    is_sufficient: false,
    knowledge_gap: 'Need more technical details about computational requirements and performance metrics',
    follow_up_queries: [
      'Transformer model computational requirements',
      'LLM performance benchmarks and metrics'
    ]
  }
];

// Sample sources
export const testSources: Source[] = [
  {
    label: 'MIT Technology Review',
    shortUrl: 'https://short.url/mit-1',
    value: 'https://www.technologyreview.com/2024/01/15/artificial-intelligence-overview'
  },
  {
    label: 'Nature Machine Intelligence',
    shortUrl: 'https://short.url/nature-1',
    value: 'https://www.nature.com/articles/s42256-024-00789-1'
  },
  {
    label: 'OpenAI Research',
    shortUrl: 'https://short.url/openai-1',
    value: 'https://openai.com/research/transformer-improvements-2024'
  },
  {
    label: 'Google AI Blog',
    shortUrl: 'https://short.url/google-1',
    value: 'https://ai.googleblog.com/2024/01/advances-in-large-language-models.html'
  },
  {
    label: 'Stanford HAI',
    shortUrl: 'https://short.url/stanford-1',
    value: 'https://hai.stanford.edu/news/ai-safety-research-2024'
  }
];

// Sample citations
export const testCitations: Citation[] = [
  {
    startIndex: 0,
    endIndex: 25,
    segments: [testSources[0]]
  },
  {
    startIndex: 50,
    endIndex: 75,
    segments: [testSources[1], testSources[2]]
  },
  {
    startIndex: 100,
    endIndex: 120,
    segments: [testSources[3]]
  }
];

// Sample web search results
export const testWebSearchResults: WebSearchResult[] = [
  {
    content: 'Artificial Intelligence (AI) refers to the simulation of human intelligence in machines [MIT Technology Review](https://short.url/mit-1). Modern AI systems use machine learning algorithms to process data and make decisions.',
    sources: [testSources[0]],
    searchQuery: 'artificial intelligence definition'
  },
  {
    content: 'Transformer neural networks revolutionized natural language processing through their attention mechanism [Nature Machine Intelligence](https://short.url/nature-1). The computational complexity scales quadratically with sequence length [OpenAI Research](https://short.url/openai-1).',
    sources: [testSources[1], testSources[2]],
    searchQuery: 'transformer neural networks attention mechanism'
  }
];

// Sample final answers
export const testFinalAnswers: FinalAnswer[] = [
  {
    answer: 'Artificial Intelligence (AI) is a field of computer science focused on creating systems that can perform tasks typically requiring human intelligence [MIT Technology Review](https://short.url/mit-1). These systems use various techniques including machine learning, neural networks, and deep learning to process information and make decisions.',
    sources: [testSources[0], testSources[1]],
    confidence: 0.92
  },
  {
    answer: 'Transformer neural networks use attention mechanisms to process sequential data more effectively than previous architectures [Nature Machine Intelligence](https://short.url/nature-1). However, their computational complexity presents challenges for scaling to very large models [OpenAI Research](https://short.url/openai-1).',
    sources: [testSources[1], testSources[2]],
    confidence: 0.88
  }
];

// Sample messages
export const testMessages = {
  simple: [
    { content: 'What is AI?', _getType: () => 'human' as const },
  ],
  conversation: [
    { content: 'What is machine learning?', _getType: () => 'human' as const },
    { content: 'Machine learning is a subset of AI...', _getType: () => 'ai' as const },
    { content: 'Can you give me some examples?', _getType: () => 'human' as const },
  ],
  complex: [
    { content: 'I need to understand how transformer models work for my research project.', _getType: () => 'human' as const },
    { content: 'Transformers are neural network architectures...', _getType: () => 'ai' as const },
    { content: 'What about the computational requirements?', _getType: () => 'human' as const },
  ]
};

// Sample Gemini API responses
export const testGeminiResponses = {
  searchQueries: {
    candidates: [{
      content: { parts: [{ text: JSON.stringify(testSearchQueries[0]) }] },
      groundingMetadata: {
        groundingSupports: [],
        groundingChunks: []
      }
    }]
  },
  
  webSearch: {
    candidates: [{
      content: { parts: [{ text: testWebSearchResults[0].content }] },
      groundingMetadata: {
        groundingSupports: [
          {
            segment: { startIndex: 0, endIndex: 25 },
            groundingChunkIndices: [0]
          }
        ],
        groundingChunks: [
          {
            web: {
              uri: testSources[0].value,
              title: testSources[0].label + '.pdf'
            }
          }
        ]
      }
    }]
  },

  reflection: {
    candidates: [{
      content: { parts: [{ text: JSON.stringify(testReflections[1]) }] },
      groundingMetadata: {
        groundingSupports: [],
        groundingChunks: []
      }
    }]
  },

  finalAnswer: {
    candidates: [{
      content: { parts: [{ text: testFinalAnswers[0].answer }] },
      groundingMetadata: {
        groundingSupports: [
          {
            segment: { startIndex: 0, endIndex: 50 },
            groundingChunkIndices: [0, 1]
          }
        ],
        groundingChunks: [
          {
            web: {
              uri: testSources[0].value,
              title: testSources[0].label + '.pdf'
            }
          },
          {
            web: {
              uri: testSources[1].value,
              title: testSources[1].label + '.pdf'
            }
          }
        ]
      }
    }]
  }
};

// Sample state objects
export const testStates = {
  initial: {
    messages: testMessages.simple,
    searchQuery: [],
    webResearchResult: [],
    sourcesGathered: [],
    initialSearchQueryCount: 3,
    maxResearchLoops: 2,
    researchLoopCount: 0,
    reasoningModel: 'test-model'
  },

  withQueries: {
    messages: testMessages.simple,
    searchQuery: ['AI definition', 'machine learning basics'],
    webResearchResult: [],
    sourcesGathered: [],
    initialSearchQueryCount: 3,
    maxResearchLoops: 2,
    researchLoopCount: 0,
    reasoningModel: 'test-model'
  },

  withResults: {
    messages: testMessages.simple,
    searchQuery: ['AI definition'],
    webResearchResult: [testWebSearchResults[0].content],
    sourcesGathered: testSources.slice(0, 2),
    initialSearchQueryCount: 3,
    maxResearchLoops: 2,
    researchLoopCount: 1,
    reasoningModel: 'test-model'
  }
};

// Helper functions for creating test data
export const createTestMessage = (content: string, type: 'human' | 'ai' = 'human') => ({
  content,
  _getType: () => type,
  id: Math.random().toString(36),
  name: undefined,
  additional_kwargs: {}
});

export const createTestSource = (index: number): Source => ({
  label: `Test Source ${index}`,
  shortUrl: `https://test.com/short/${index}`,
  value: `https://test.com/original/${index}`
});

export const createTestCitation = (startIndex: number, endIndex: number, sourceCount: number = 1): Citation => ({
  startIndex,
  endIndex,
  segments: Array.from({ length: sourceCount }, (_, i) => createTestSource(i + 1))
});

export const createTestConfig = (overrides: Partial<AgentConfig> = {}): AgentConfig => ({
  ...testConfigs.default,
  ...overrides
});
